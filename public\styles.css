/* Global styles */
* {
    box-sizing: border-box;
}

html {
    height: 100%;
    overflow-y: auto;
}

body {
    font-family: 'Inter', sans-serif;
    background-color: #121212;
    color: #fff;
    margin: 0;
    padding: 0;
    min-height: 100vh;
    overflow-x: hidden;
    overflow-y: auto;
    position: relative;
}

.control-hint {
    font-size: 0.8rem;
    color: #8bb4f7;
    margin-top: 4px;
    font-style: italic;
}

/* Topbar styles */
.topbar {
    background-color: rgb(255, 255, 255);
    padding: 0.75rem 0;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.topbar-content {
    max-width: 1000px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 40px;
    padding: 0 1rem;
}

.topbar-left {
    display: flex;
    align-items: center;
    flex: 1;
}

.topbar-center {
    display: flex;
    gap: 1rem;
    flex: 22;
    justify-content: center;
}

.topbar-right {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex: 1;
    justify-content: flex-end;
}

.logo {
    color: #ffffff;
    text-decoration: none;
    font-size: 1.25rem;
    font-weight: bold;
    display: flex;
    align-items: center;
}

.nav-link {
    color: #ffffff;
    text-decoration: none;
    font-size: 0.9375rem;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    background: #3b82f6;
    transition: background-color 0.2s;
}

.nav-link:hover {
    background: rgba(255, 255, 255, 0.15);
}

.nav-link.active {
    background: rgba(255, 255, 255, 0.2);
}

/* User menu styles */
.user-menu-button {
    background: none;
    border: none;
    padding: 0;
    cursor: pointer;
    height: 40px;
    width: 40px;

    align-items: center;
    justify-content: center;
}

.credits-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 6px 12px;
    background: rgb(78 175 88);
    color: #ffffff;
    border-radius: 8px;
    font-weight: 500;
}

.credits-indicator i {
    color: #ffd700;
}

.btn-upgrade {
    background: #4CAF50;
    color: white;
    border: none;
    padding: 10px 20px;
    height: 30px;
    font-size: 12px;
    border-radius: 8px;
    cursor: pointer;
    transition: background-color 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
    width: 120px;
}

.btn-upgrade:hover {
    background: #45a049;
}

.user-avatar {
    width: 32px;
    height: 32px;
    background: #2a2a2a;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
    font-size: 14px;
}

/* User dropdown menu styles */
.user-dropdown {
    background-color: #1a1a1a;
    border: 1px solid #333;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    padding: 8px 0;
    min-width: 220px;
}

.user-dropdown a {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 10px 16px;
    color: #fff;
    text-decoration: none;
    font-size: 14px;
    transition: background-color 0.2s;
    position: relative;
}

.user-dropdown a:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

.user-dropdown a::before {
    content: '';
    width: 20px;
    height: 20px;
    background-size: 20px;
    background-repeat: no-repeat;
    background-position: center;
    opacity: 0.7;
}

/* Icons for menu items */
.user-dropdown a[href="/profile"]::before {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='white'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z'/%3E%3C/svg%3E");
}

.user-dropdown a[href="/profile?tab=subscription"]::before {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='white'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M13 10V3L4 14h7v7l9-11h-7z'/%3E%3C/svg%3E");
}

.user-dropdown a[href="/logout"]::before {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%23ff4d4d'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1'/%3E%3C/svg%3E");
}

/* Credits display at the top */
.user-dropdown::before {
    content: attr(data-credits);
    display: block;
    padding: 12px 16px;
    margin-bottom: 8px;
    border-bottom: 1px solid #333;
    color: #999;
    font-size: 14px;
}

/* Logout link special styling */
.user-dropdown a[href="/logout"] {
    color: #ff4d4d;
    margin-top: 8px;
    border-top: 1px solid #333;
    padding-top: 12px;
}

/* Adjust main content for sticky header */
main, .container {
    margin-top: 80px; /* Space for sticky topbar */
    padding: 20px;
    padding-bottom: 60px; /* Extra bottom padding for scroll */
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
    min-height: calc(100vh - 80px); /* Ensure minimum height for scrolling */
}

/* User dropdown styles */
.user-dropdown {
    background-color: #1a1a1a;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: absolute;
    top: 100%;
    right: 0;
    width: 200px;
    margin-top: 0.5rem;
    z-index: 1001;
}

/* Navigation Menu */
.nav-menu {
    display: flex;
    gap: 20px;
    align-items: center;
}

.nav-link {
    color: #fff;
    text-decoration: none;
    padding: 8px 16px;
    border-radius: 4px;
    transition: background-color 0.3s;
    font-weight: 500;
}

.nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.nav-link.active {
    background-color: rgba(255, 255, 255, 0.2);
}

.logo {
    color: #ffffff;
    text-decoration: none;
    font-size: 1.2rem;
    font-weight: bold;
}

.nav-link {
    color: #ffffff;
    text-decoration: none;
    font-size: 0.8rem;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.btn-login, .btn-signup {
    color: #ffffff;
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.btn-login:hover, .btn-signup:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

/* Topbar dropdown styles */
.dropdown-divider {
    height: 1px;
    background-color: rgba(255, 255, 255, 0.1);
    margin: 8px 0;
}

.dropdown-section-title {
    font-size: 0.8rem;
    color: #999;
    padding: 8px 16px 4px;
    text-transform: capitalize;
    letter-spacing: 0.5px;
    font-weight: 600;
}

/* Collections Page */
.collections-container {
    margin-top: 80px; /* Space for sticky topbar */
    padding: 20px;
    padding-bottom: 60px; /* Extra bottom padding for scroll */
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
    min-height: calc(100vh - 80px); /* Ensure minimum height for scrolling */
}

/* Admin Tables - Ensure proper scrolling */
.styles-table, .inspirations-table {
    width: 100%;
    margin-bottom: 40px; /* Extra space at bottom */
}

/* Admin Forms - Ensure proper spacing */
.add-style-form, .settings-section {
    margin-bottom: 40px; /* Extra space between sections */
}

/* Ensure content doesn't get cut off */
.container:last-child,
.collections-container:last-child {
    padding-bottom: 80px; /* Extra padding for last container */
}

/* Force scrolling on admin pages */
html {
    height: 100% !important;
    overflow-y: scroll !important;
    scroll-behavior: smooth;
}

html, body {
    height: auto !important;
    min-height: 100vh !important;
    overflow-y: scroll !important;
    overflow-x: hidden !important;
}

/* Specific fixes for admin pages */
body {
    max-height: none !important;
    height: auto !important;
}

/* Ensure tables and content can scroll */
.inspirations-table, .styles-table {
    overflow: visible !important;
    height: auto !important;
}

/* Fix for modal overlays that might block scrolling */
.modal {
    overflow-y: auto !important;
}

/* Ensure page content has proper height */
.container {
    height: auto !important;
    max-height: none !important;
}

/* Emergency scroll fix - very specific selectors */
html[lang="en"] {
    overflow-y: scroll !important;
    height: auto !important;
}

html[lang="en"] body {
    overflow-y: visible !important;
    height: auto !important;
    position: static !important;
}

/* Force scroll on specific admin pages */
body:has(.inspirations-table),
body:has(.styles-table),
body:has(.collections-grid) {
    overflow-y: scroll !important;
    height: auto !important;
    min-height: 100vh !important;
}

/* Ensure no element blocks scrolling */
/* * {
    max-height: none !important;
}

/* Override any fixed heights */
#topbar ~ .container,
#topbar ~ .collections-container {
    height: auto !important;
    overflow: visible !important;
}

.collections-section,
.generations-section {
    margin-bottom: 40px;
}

.collections-grid,
.generations-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 2rem;
    padding: 1rem;
}

.collection-grid-container {
    position: relative;
    padding-top: 75%;
    background: #1a1a1a;
    display: grid;
    grid-template-columns: 2fr 1fr;
    grid-template-rows: repeat(2, 1fr);
    gap: 4px;
}

.main-thumbnail {
    position: absolute;
    top: 0;
    left: 0;
    width: calc(66.66% - 2px);
    height: 100%;
    background: #333;
    border-radius: 4px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
}

.main-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.small-thumbnails {
    position: absolute;
    top: 0;
    right: 0;
    width: calc(33.33% - 2px);
    height: 100%;
    display: grid;
    grid-template-rows: repeat(3, 1fr);
    gap: 4px;
}

.small-thumbnail {
    background: #333;
    border-radius: 4px;
    overflow: hidden;
    position: relative;
}

.small-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.empty {
    display: flex;
    align-items: center;
    justify-content: center;
    color: #666;
}

.collection-card {
    background: #2a2a2a;
    border-radius: 12px;
    overflow: hidden;
    text-decoration: none;
    color: white;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    display: block;
    height: 100%;
}

.collection-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.collection-info {
    padding: 16px;
}

.collection-title {
    margin: 0;
    font-size: 1.1em;
    font-weight: 600;
    color: white;
}

.collection-count {
    margin-top: 4px;
    font-size: 0.9em;
    color: #999;
}

.new-collection {
    border: 2px dashed #444;
    background: transparent;
    cursor: pointer;
    transition: border-color 0.2s ease, background-color 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 200px;
}

.new-collection:hover {
    border-color: #666;
    background: rgba(255, 255, 255, 0.05);
}

.collection-placeholder {
    text-align: center;
    color: #666;
}

.collection-placeholder span {
    font-size: 2em;
    display: block;
    margin-bottom: 8px;
}

.collection-placeholder p {
    margin: 0;
    font-size: 1.1em;
}

/* Placeholder Grid */
.placeholder-grid {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4px;
    padding: 4px;
}

.main-placeholder {
    grid-row: 1 / span 2;
    position: relative !important;
    height: 100%;
    width: 100%;
}

.right-placeholders {
    display: grid;
    grid-template-rows: 1fr 1fr;
    gap: 4px;
}

/* Image Placeholders */
.image-placeholder {
    background-color: #404040;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    width: 100%;
    height: 100%;
}

.image-placeholder::after {
    content: '';
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cpath fill='%23666' d='M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z'/%3E%3C/svg%3E");
    width: 24px;
    height: 24px;
    opacity: 0.5;
}

.small-placeholder {
    position: relative;
    width: 100%;
    height: 100%;
}

.options-icon {
    position: absolute;
    top: 4px;
    right: 4px;
    width: 20px;
    height: 20px;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 3px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-size: 14px;
    cursor: pointer;
    z-index: 1;
}

.options-icon:hover {
    background-color: rgba(0, 0, 0, 0.7);
}

.collection-info {
    padding: 12px;
}

.collection-title {
    color: #fff;
    font-size: 14px;
    margin: 0;
}

.collection-count {
    color: #888;
    font-size: 12px;
    margin-top: 4px;
}

.new-collection {
    border: 2px dashed #666;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px;
    min-height: 200px;
}

.new-collection .collection-icon {
    width: 48px;
    height: 48px;
    margin-bottom: 12px;
    color: #666;
}

.new-collection:hover {
    border-color: #888;
    background-color: #333;
}

/* Generation Card */
.generation-card {
    position: relative;
    border-radius: 8px;
    background-color: #2a2a2a;
    overflow: hidden;
    margin-bottom: 20px;
}

.image-wrapper {
    position: relative;
    width: 100%;
    padding-bottom: 100%; /* Square aspect ratio */
    overflow: hidden;
}

.image-wrapper img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: contain;
    background-color: #1a1a1a;
}

.button-wrapper {
    padding: 12px;
    display: flex;
    justify-content: center;
    background-color: #2a2a2a;
}

.add-to-collection-btn {
    background: transparent;
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #fff;
    padding: 8px 16px;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    gap: 8px;
    font-family: 'Inter', sans-serif;
}

.add-to-collection-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.3);
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    align-items: center;
    justify-content: center;
}

.modal.active {
    display: flex;
}

.modal-content {
    background-color: #1a1a1a;
    padding: 24px;
    border-radius: 8px;
    width: 100%;
    max-width: 400px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.modal-content h2 {
    margin: 0 0 20px 0;
    color: #fff;
}

.form-group {
    margin-bottom: 20px;
}

.form-group input {
    width: 100%;
    padding: 10px;
    border: 1px solid #333;
    border-radius: 4px;
    background-color: #2a2a2a;
    color: #fff;
    font-size: 16px;
}

.form-group input:focus {
    outline: none;
    border-color: #666;
}

.modal-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.btn-primary,
.btn-secondary {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.3s;
}

.btn-primary {
    background-color: #0066cc;
    color: white;
}

.btn-primary:hover {
    background-color: #0052a3;
}

.btn-secondary {
    background-color: #333;
    color: white;
}

.btn-secondary:hover {
    background-color: #444;
}

/* Collection Selector Modal */
.collection-selector {
    width: 300px;
    max-height: 400px;
    display: flex;
    flex-direction: column;
}

.collections-list {
    flex-grow: 1;
    overflow-y: auto;
    margin: 16px 0;
}

.collection-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    cursor: pointer;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.collection-item:hover {
    background-color: #3a3a3a;
}

.collection-title {
    color: #fff;
    font-size: 14px;
}

.collection-count {
    color: #888;
    font-size: 12px;
}

.new-collection-option {
    border-top: 1px solid #3a3a3a;
    padding-top: 12px;
}

.new-collection-button {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #fff;
    padding: 8px;
    cursor: pointer;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.new-collection-button:hover {
    background-color: #3a3a3a;
}

.new-collection-button svg {
    width: 16px;
    height: 16px;
}

/* Collection Details Page */
.collection-header {
    margin: 2rem 0;
    text-align: center;
}

.collection-header h1 {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.collection-header p {
    color: #666;
    font-size: 1.1rem;
}

.images-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 2rem;
    padding: 1rem;
}

.image-container {
    background: #1a1a1a;
    border-radius: 12px;
    padding: 1rem;
    transition: transform 0.2s;
}

.image-container:hover {
    transform: translateY(-5px);
}

.image-container img {
    width: 100%;
    height: auto;
    border-radius: 8px;
    margin-bottom: 1rem;
}

.image-prompt {
    color: #ccc;
    font-size: 0.9rem;
    margin: 0;
    text-align: center;
}

/* Image Card Styles */
.image-wrapper {
    position: relative;
    border-radius: 12px;
    overflow: hidden;
}

.image-wrapper img {
    width: 100%;
    height: auto;
    display: block;
}

/* Hover Effect */
.hover-effect {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.4);
    opacity: 0;
    transition: opacity 0.2s ease;
}

.image-wrapper:hover .hover-effect {
    opacity: 1;
}

/* Menu Button */
.menu-button {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 32px;
    height: 32px;
    border-radius: 6px;
    background: rgba(137, 137, 137,.6);
    border: none;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.2s ease;
}

.menu-button:hover,
.menu-button.active {
    background: rgba(255, 255, 255, 0.9);
    color: #000;
}

.image-wrapper:hover .menu-button {
    opacity: 1;
}

/* Menu Dropdown */
.menu-dropdown {
    position: absolute;
    top: 44px;
    right: 8px;
    background: #2a2a2a;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    padding: 8px;
    display: none;
    min-width: 200px;
    z-index: 1000;
}

.menu-dropdown.active {
    display: block;
}

.menu-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    width: 100%;
    border: none;
    background: none;
    color: #fff;
    cursor: pointer;
    border-radius: 4px;
    text-align: left;
}

.menu-item:hover {
    background: rgba(255, 255, 255, 0.1);
}

.menu-item.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.menu-item.text-danger {
    color: #ff4444;
}

.menu-item.text-danger:hover {
    background: rgba(255, 68, 68, 0.1);
}

.menu-item i {
    width: 16px;
    color: #666;
}

.menu-separator {
    height: 1px;
    background: rgba(255, 255, 255, 0.1);
    margin: 8px 0;
}

/* Generation Card */
.generation-card {
    position: relative;
    border-radius: 12px;
    overflow: hidden;
    background: #1a1a1a;
}

.generation-card img {
    width: 100%;
    height: auto;
    display: block;
    border-radius: 12px;
}

/* Profile Section */
.profile-section {
    position: relative;
}

.profile-trigger {
    cursor: pointer;
}

.profile-avatar {
    width: 40px;
    height: 40px;
    background: #4CAF50;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 1rem;
}

/* Dropdown Menu */
.profile-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    width: 300px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    margin-top: 0.5rem;
    display: none;
    z-index: 1000;
}

.profile-dropdown.show {
    display: block;
}

.dropdown-header {
    padding: 1rem;
    border-bottom: 1px solid #eee;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.user-details {
    flex: 1;
}

.user-name {
    font-weight: bold;
    color: #333;
}

.user-email {
    color: #666;
    font-size: 0.9rem;
}

.upgrade-banner {
    padding: 1rem;
    background: #f8f9fa;
    border-bottom: 1px solid #eee;
}

.banner-content {
    background: #fff;
    border: 1px solid #eee;
    border-radius: 8px;
    padding: 1rem;
}

.plan-info {
    margin-bottom: 0.5rem;
}

.plan-name {
    font-weight: bold;
    color: #333;
}

.plan-description {
    color: #666;
    font-size: 0.9rem;
    margin-top: 0.25rem;
}

.upgrade-button {
    width: 100%;
    background: #4CAF50;
    color: white;
    border: none;
    padding: 0.5rem;
    border-radius: 4px;
    cursor: pointer;
    margin-top: 0.5rem;
}

.upgrade-button:hover {
    background: #45a049;
}

.dropdown-menu {
    padding: 0.5rem 0;
}

.menu-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    color: #333;
    text-decoration: none;
    transition: background-color 0.2s;
}

.menu-item:hover {
    background: #f8f9fa;
}

.menu-item i {
    width: 20px;
    color: #666;
}

.dropdown-footer {
    padding: 0.5rem 0;
    border-top: 1px solid #eee;
}

/* Profile Page */
.profile-page {
    max-width: 800px;
    margin: 2rem auto;
    padding: 0 1rem;
}

.profile-tabs {
    display: flex;
    gap: 2rem;
    border-bottom: 1px solid #eee;
    margin-bottom: 2rem;
}

.profile-tab {
    padding: 1rem 0;
    color: #666;
    text-decoration: none;
    position: relative;
}

.profile-tab.active {
    color: #4CAF50;
}

.profile-tab.active::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    right: 0;
    height: 2px;
    background: #4CAF50;
}

.profile-section {
    margin-bottom: 2rem;
}

.profile-section h2 {
    margin-bottom: 1rem;
    color: #333;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: #888888;
}

.form-group input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #272727;
    border-radius: 4px;
    font-size: 1rem;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
}

.btn {
    padding: 0.75rem 1.5rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 1rem;
}

.btn-primary {
    background: #4CAF50;
    color: white;
    border: none;
}

.btn-secondary {
    background: white;
    color: #333;
    border: 1px solid #ddd;
}

.error {
    color: #ff4d4d;
    margin: 1rem 0;
}

.style-section {
    margin: 2rem 0;
}

.style-section h3 {
    color: #fff;
    margin-bottom: 1rem;
}

#result {
    margin-top: 2rem;
}

#result img {
    max-width: 100%;
    border-radius: 8px;
}

.actions {
    margin-top: 1rem;
    display: flex;
    gap: 1rem;
}

/* Image Card Styles */
.image-card {
    position: relative;
    border-radius: 12px;
    overflow: hidden;
    background: #1a1a1a;
    margin-bottom: 20px;
}

.image-container {
    position: relative;
    width: 100%;
}

.image-container img {
    width: 100%;
    height: auto;
    display: block;
}

.menu-button {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.5);
    border: none;
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    opacity: 0;
    transition: opacity 0.2s;
}

.image-container:hover .menu-button {
    opacity: 1;
}

.context-menu {
    position: absolute;
    top: 45px;
    right: 10px;
    background: #2a2a2a;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    z-index: 1000;
    min-width: 150px;
}

.menu-item {
    padding: 8px 16px;
    color: white;
    cursor: pointer;
    transition: background-color 0.2s;
}

.menu-item:hover {
    background: #3a3a3a;
}

.menu-item:first-child {
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
}

.menu-item:last-child {
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;
}

/* Toast Notification Styles */
.toast-notification {
    pointer-events: none;
    position: fixed;
    z-index: 10000;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    z-index: 1000;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.modal.active {
    display: flex;
    opacity: 1;
    justify-content: center;
    align-items: center;
}

.modal-content {
    background: #1E1E1E;
    border-radius: 12px;
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.modal-header {
    padding: 20px;
    border-bottom: 1px solid #333;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h2 {
    margin: 0;
    font-size: 1.5rem;
    color: white;
}

.close-modal {
    background: none;
    border: none;
    color: #999;
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    line-height: 1;
}

.close-modal:hover {
    color: white;
}

.modal-body {
    padding: 20px;
}

.collections-list {
    margin: 15px 0;
    display: grid;
    gap: 10px;
}

.collection-item {
    padding: 10px;
    background: #2a2a2a;
    border-radius: 6px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.collection-item:hover {
    background: #333;
}

.divider {
    text-align: center;
    margin: 20px 0;
    color: #666;
    position: relative;
}

.divider::before,
.divider::after {
    content: '';
    position: absolute;
    top: 50%;
    width: 45%;
    height: 1px;
    background: #333;
}

.divider::before {
    left: 0;
}

.divider::after {
    right: 0;
}

#newCollectionName {
    width: 100%;
    padding: 8px 12px;
    background: #2a2a2a;
    border: 1px solid #333;
    border-radius: 6px;
    color: white;
    margin: 10px 0;
}

.create-collection {
    width: 100%;
    margin-top: 10px;
}

.style-list {
    margin: 2rem 0;
}

.carousel-container {
    position: relative;
    width: 100%;
    overflow: hidden;
    padding: 0 40px;
}

.style-carousel {
    display: flex;
    gap: 1rem;
    overflow-x: auto;
    scroll-behavior: smooth;
    scrollbar-width: none;
    -ms-overflow-style: none;
    padding: 1rem 0;
}

.style-carousel::-webkit-scrollbar {
    display: none;
}

.style-item {
    flex: 0 0 200px;
    border-radius: 8px;
    overflow: hidden;
    cursor: pointer;
    transition: transform 0.2s;
    border: 2px solid transparent;
}

.style-item:hover {
    transform: translateY(-2px);
}

.style-item.selected {
    border-color: #4CAF50;
}

.style-item img {
    width: 100%;
    height: 150px;
    object-fit: cover;
}

.style-name {
    padding: 0.5rem;
    text-align: center;
    background: rgba(0, 0, 0, 0.8);
    color: white;
}

.carousel-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 50%;
    background: rgba(0, 0, 0, 0.5);
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1;
}

.carousel-nav.prev {
    left: 0;
}

.carousel-nav.next {
    right: 0;
}

.carousel-nav:hover {
    background: rgba(0, 0, 0, 0.7);
}

.custom-select {
    display: block;
    width: 100%;
    padding: 0.5rem;
    font-size: 1rem;
    line-height: 1.5;
    color: #495057;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.custom-select:focus {
    border-color: #80bdff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}
 /* Reset and Basic Styling */
 * {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html, body {
    height: 100%;
    overflow: hidden; /* Prevent body scroll */
    font-family: 'Poppins', sans-serif; /* Example font, include if needed */
    font-size: 14px;
    background-color: #757575;
    color: #ffffff;
}

/* Studio Layout */
.studio-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
}

.studio-header {
    background-color: #1e293b; /* Dark blue/grey */
    color: #e2e8f0; /* Light grey text */
    padding: 10px 20px;
    font-size: 1.2em;
    font-weight: 600;
    flex-shrink: 0; /* Prevent header from shrinking */
}
.pickr .pcr-button {
border: 1px solid #ccc !important; /* Light grey border */
}

.main-content {
    display: flex;
    flex-grow: 1; /* Takes remaining height */
    overflow: hidden; /* Prevent this container from scrolling */
}

.canvas-area {
    flex-grow: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #d1d5db; /* Neutral grey background */
    overflow: hidden; /* Hide scrollbars, panning is manual */
    position: relative; /* Needed for positioning zoom controls */
    cursor: grab; /* Default cursor for pannable area */
}
.canvas-area.panning {
    cursor: grabbing;
}


canvas {
    border: 1px solid #9ca3af; /* Slightly darker border for contrast */
    background-color: #ffffff; /* White canvas background */
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    display: block; /* Remove extra space below */
    cursor: default; /* Override parent cursor */
}
 canvas.dragging { cursor: grabbing; } /* For object dragging */


/* Zoom Controls */
.canvas-controls {
    position: fixed;
    bottom: 15px;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 8px;
    padding: 5px 10px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.15);
    display: flex;
    align-items: center;
    gap: 8px;
    z-index: 9999; /* ensure on top */
}
.canvas-controls button {
    background: none;
    border: none;
    font-size: 1.1em;
    font-weight: bold;
    padding: 3px 6px;
    cursor: pointer;
    border-radius: 4px;
    color: #4b5563;
    transition: background-color 0.2s ease;
}
 .canvas-controls button:hover {
     background-color: #e5e7eb;
 }
 .canvas-controls .zoom-level {
     font-size: 0.9em;
     font-weight: 500;
     min-width: 45px;
     text-align: center;
     color: #374151;
 }
 .canvas-controls .separator {
    width: 1px;
    height: 15px;
    background-color: #d1d5db;
    margin: 0 4px;
 }


/* Sidebar Styling */
.sidebar {
    width: 350px;
    flex-shrink: 0;
    background-color: #ffffff;
    border-left: 1px solid #e2e8f0;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.sidebar-tabs {
    display: flex;
    border-bottom: 1px solid #e2e8f0;
    flex-shrink: 0;
}

.sidebar-tab {
    padding: 12px 15px;
    cursor: pointer;
    border: none;
    background: none;
    font-size: 0.95em;
    font-weight: 500;
    color: #64748b;
    border-bottom: 3px solid transparent;
    transition: all 0.2s ease-in-out;
     flex-grow: 1;
     text-align: center;
}

.sidebar-tab.active {
    color: #0f172a;
    border-bottom-color: #3b82f6;
}
 .sidebar-tab:hover:not(.active) {
    background-color: #f8fafc;
 }


.sidebar-content {
    flex-grow: 1;
    overflow-y: auto;
    padding: 15px;
     display: none;
}
.sidebar-content.active {
    display: block;
}

/* Common Button Styling */
 .action-buttons {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 15px;
    flex-shrink: 0;
}
 .action-buttons button {
     padding: 8px 15px;
     border: none;
     border-radius: 6px;
     cursor: pointer;
     font-size: 0.9em;
     font-weight: 500;
     transition: background-color 0.2s ease;
     margin-left: 5px;
 }
.action-buttons .add-btn {
     background-color: #3b82f6;
     color: white;
     min-width: 80px;
 }
 .action-buttons .add-btn:hover { background-color: #2563eb; }
 .action-buttons .delete-btn {
     background-color: #ef4444;
     color: white;
 }
 .action-buttons .delete-btn:hover:enabled { background-color: #dc2626; }
 .action-buttons .delete-btn:disabled {
     background-color: #d1d5db;
     cursor: not-allowed;
 }

/* Text/Image Headers */
.text-properties-header, .image-properties-header {
     font-size: 1.1em;
     font-weight: 600;
     margin-bottom: 10px;
     color: #1e293b;
     border-bottom: 1px solid #e2e8f0;
     padding-bottom: 8px;
     flex-shrink: 0;
}


/* Text Property Tabs */
.text-property-tabs {
    display: flex;
    margin-bottom: 15px;
    background-color: #f1f5f9;
    border-radius: 8px;
    padding: 4px;
    flex-shrink: 0;
}
.property-tab {
     flex: 1;
     text-align: center;
     padding: 8px 5px;
     cursor: pointer;
     border: none;
     background-color: transparent;
     font-size: 0.85em;
     font-weight: 500;
     color: #475569;
     border-radius: 6px;
     transition: all 0.2s ease-in-out;
}
.property-tab.active {
    background-color: #ffffff;
    color: #0f172a;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}
.property-tab:hover:not(.active) { background-color: #e2e8f0; }

.property-panel {
    display: none;
    flex-direction: column;
    gap: 12px;
}
.property-panel.active { display: flex; }

/* Control Group Styling */
.control-group {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 0;
    width: 100%;
    flex-shrink: 0;
}
.control-group label,
.parameter-control > label,
.effect-control > div label,
.control-group .radio-group label {
     flex-basis: 100px;
     flex-shrink: 0;
     text-align: right;
     font-size: 0.85em;
     color: #475569;
     margin-right: 0;
     width: auto;
}
.slider-container {
     flex-grow: 1;
     display: flex;
     align-items: center;
}
input[type="range"] {
    flex-grow: 1;
    height: 4px;
    cursor: pointer;
    width: auto;
    vertical-align: middle;
}
 span.slider-value {
     font-size: 0.85em;
     color: #64748b;
     text-align: right;
     min-width: 40px;
     margin-left: 8px;
 }
input[type="text"], select {
    flex-grow: 1;
    padding: 6px 10px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 0.9em;
    width: auto;
}
select#iFontFamily option {
     font-size: 16px;
     padding: 4px 8px;
}
input[type="text"]:focus, select:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3);
}
input[type="color"] {
    width: 40px;
    height: 30px;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    padding: 2px;
    cursor: pointer;
    vertical-align: middle;
    margin-left: 0;
}
 .simplified-color-picker { flex-grow: 1; display: flex; }

/* Checkbox/Radio Styling */
 input[type="checkbox"], input[type="radio"] { margin-right: 5px; cursor: pointer; }
 .font-style-controls { display: flex; align-items: center; gap: 15px; margin-left: auto; }
 .font-style-controls label { flex-basis: auto; text-align: left; margin-right: 2px;}
 .font-style-controls input { margin-right: 8px;}
 .radio-group { display: flex; align-items: center; gap: 10px; }
 .radio-container { display: flex; align-items: center; }
 .radio-container label { width: auto; text-align: left; margin: 0; font-size: 0.9em; }
 .radio-container span { margin-left: 3px; font-size: 0.9em; color: #333; }
 .fill-direction label:first-child { flex-basis: 100px; flex-shrink: 0; text-align: right; }

/* Parameter Control Sections */
.parameter-control { display: none; flex-direction: column; gap: 10px; border-top: 1px dashed #e2e8f0; padding-top: 10px; margin-top: 10px; }
.parameter-control h3, .parameter-control h4 { font-size: 0.9em; font-weight: 600; color: #1e293b; margin: 5px 0; border: none; padding: 0; }
.parameter-control h4 { font-size: 0.85em; color: #475569; margin-left: 0; }
.parameter-control h5 { font-size: 0.8em; font-weight: 600; color: #334155; margin: 8px 0 2px 0; }
.normal .normal-param, .warp .warp-param, .skew .skew-param, .circle .circle-param, .curve .curve-param, .horizontalLines .horizontal-lines-param, .colorCut .color-cut-param, .obliqueLines .oblique-lines-param, .fadingLinesCut .fading-lines-cut-param, .stroke-enabled .stroke-param, .shadow .shadow-param, .block-shadow .block-shadow-param, .line-shadow .line-shadow-param, .detailed-3d .detailed-3d-param, .grid-distort .grid-distort-param { display: flex; }
.warp .horizontal-skew { display: flex; } .skew .horizontal-skew, .skew .vertical-skew { display: flex; } .triangle-warp-enabled .shift-center-control { display: flex; } .warp-param .shift-center-control:not(.triangle-warp-enabled .shift-center-control) { display: none; }
.property-panel .parameter-control { width: 100%; margin-left: 0; padding-left: 0; }
.property-panel .control-group { padding-left: 5px; }
.property-panel .parameter-control h3, .property-panel .parameter-control h4, .property-panel .parameter-control h5 { padding-left: 5px; }

/* Image Tab Specific */
#image-controls { display: none; flex-direction: column; gap: 12px; margin-top: 15px; }
#image-controls.visible { display: flex; }
#image-file-input { display: none; }

/* Style the default Pickr button */
#canvasBgColorPicker .pcr-button {
    width: 28px !important; /* Match other controls */
    height: 28px !important;
    border-radius: 4px !important;
    border: 3px solid #d1d5db !important; /* Added border */
    box-shadow: none !important; /* Remove default shadow if any */
    vertical-align: middle; /* Align with other controls */
}
/* Ensure the container div doesn't interfere */
#canvasBgColorPicker {
    display: inline-block; /* Allow vertical alignment */
    vertical-align: middle;
    line-height: 0; /* Prevent extra space */
}
