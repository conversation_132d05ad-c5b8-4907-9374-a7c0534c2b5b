<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Prompt to Sticker Generator</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <link rel="stylesheet" href="/styles.css">
    <script type="module" src="/js/components/CollectionModal.js"></script>
    <script type="module" src="/js/components/Toast.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Inter', sans-serif;
            background: #111;
            color: #fff;
        }

        .inspiration-btn {
            display: inline-block; 
            background: linear-gradient(135deg, #4a00e0, #8e2de2); 
            color: #fff; 
            padding: 12px 20px; 
            border-radius: 8px; 
            text-decoration: none; 
            transition: all 0.3s; 
            font-weight: 500; 
            box-shadow: 0 4px 10px rgba(0,0,0,0.2);
        }
        
        .inspiration-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 15px rgba(0,0,0,0.3);
            background: linear-gradient(135deg, #5b12f3, #9d3cf3);
        }

        .topbar-wrapper {
            background-color: #1a1a1a;
            padding: 10px 0;
        }

        #topbar {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            height: 50px;
        
            justify-content: space-between;
            align-items: center;
        }

        #topbar .left {
            display: flex;
            align-items: center;
            gap: 2rem;
        }

        #topbar .right {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .container {
            max-width: 800px;
            margin: 1rem auto 2rem;
            padding: 0 1rem;
        }
        
        .generator-form {
            margin-bottom: 2rem;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #fff;
        }
        
        textarea {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #333;
            border-radius: 8px;
            min-height: 100px;
            font-family: inherit;
            background: #1a1a1a;
            color: #fff;
            resize: vertical;
        }

        textarea:focus {
            outline: none;
            border-color: #4CAF50;
        }
        
        button {
            width: 100%;
            background-color: #4CAF50;
            color: white;
            padding: 0.5rem 1.5rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 500;
            transition: background-color 0.2s;
            margin-top: 0.1rem;
        }
        
        button:hover {
            background-color: #45a049;
        }

        button:disabled {
            background-color: #666;
            cursor: not-allowed;
        }
        
        .result {
            margin-top: 2rem;
        }

        .generation-container {
            position: relative;
            width: 100%;
            max-width: 512px;
            margin: 0 auto;
            border-radius: 8px;
            overflow: hidden;
        }

        .generation-image {
            width: 100%;
            height: auto;
            display: block;
        }

        generation-card {
            display: block;
            width: 100%;
            max-width: 512px;
            margin: 0 auto;
        }

        .loading {
            text-align: center;
            padding: 20px;
            color: #999;
        }

        .error {
            text-align: center;
            padding: 20px;
            color: #ff6b6b;
        }

        .menu-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.7);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 10px;
            opacity: 0;
            transition: opacity 0.3s ease;
            pointer-events: none;
        }

        .generation-container:hover .menu-overlay {
            opacity: 1;
            pointer-events: auto;
        }

        .menu-button {
            background: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            color: #333;
            transition: background-color 0.2s;
            width: 160px;
            justify-content: center;
        }

        .menu-button:hover {
            background: #f0f0f0;
        }

        .menu-button i {
            font-size: 16px;
        }

        .generations-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 1rem;
            margin-top: 2rem;
        }

        .recent-generations {
            margin-top: 3rem;
        }

        .recent-generations h2 {
            color: #fff;
            margin-bottom: 1rem;
        }

        .generation-card {
            margin: 20px 0;
            border-radius: 12px;
            overflow: hidden;
            background: #1a1a1a;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .generation-card .card {
            position: relative;
        }

        .generation-card .image-container {
            width: 100%;
            aspect-ratio: 1;
            overflow: hidden;
            position: relative;
        }

        .generation-card img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            display: block;
        }

        .generation-card .image-options-dropdown {
            position: absolute;
            top: 10px;
            right: 10px;
        }

        .generation-card .dropdown-toggle {
            background: rgba(0, 0, 0, 0.5);
            border: none;
            color: white;
            padding: 8px;
            border-radius: 50%;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .generation-card .dropdown-toggle:hover {
            background: rgba(0, 0, 0, 0.7);
        }

        .generation-card .dropdown-menu {
            position: absolute;
            right: 0;
            top: 100%;
            background: #2a2a2a;
            border-radius: 8px;
            padding: 8px 0;
            margin-top: 4px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            display: none;
        }

        .generation-card .dropdown-toggle:focus + .dropdown-menu,
        .generation-card .dropdown-menu:hover {
            display: block;
        }

        .generation-card .dropdown-menu a {
            display: block;
            padding: 8px 16px;
            color: white;
            text-decoration: none;
            transition: background-color 0.2s;
        }

        .generation-card .dropdown-menu a:hover {
            background: #3a3a3a;
        }

        .user-menu {
            position: relative;
        }

        .user-menu-button {
            background: none;
            border: none;
            padding: 0;
            cursor: pointer;
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            background: #4CAF50;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }

        .user-dropdown {
            display: none;
            position: absolute;
            top: 100%;
            right: 0px;
            background-color: #1a1a1a;
            border-radius: 8px;
            box-shadow: rgba(0, 0, 0, 0.1) 0px 2px 16px;
            width: 200px;
            margin-top: 0.5rem;
        }

        .user-dropdown-content {
            padding: 16px;
        }

        .credits-indicator {
            display: flex;
            align-items: center;
            gap: 4px;
            color: white;
            font-weight: bold;
        }

        .credits-icon {
            font-size: 1.5em;
        }

        .btn-upgrade {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .btn-upgrade:hover {
            background: #45a049;
        }

        .collections-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 24px;
            margin: 24px 0;
        }

        .collection-card {
            background: #1a1a1a;
            border-radius: 12px;
            overflow: hidden;
            cursor: pointer;
            transition: transform 0.2s;
        }

        .collection-card:hover {
            transform: translateY(-4px);
        }

        .collection-preview {
            aspect-ratio: 1;
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 2px;
            background: #1a1a1a;
            border-radius: 8px 8px 0 0;
            overflow: hidden;
        }

        .preview-image {
            aspect-ratio: 1;
            position: relative;
            background: #222;
            overflow: hidden;
        }

        .preview-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .preview-image.empty {
            background: #2a2a2a;
        }

        .preview-image.error {
            background: #442;
        }

        .collection-info {
            padding: 12px;
            background: #1a1a1a;
            border-radius: 0 0 8px 8px;
        }

        .collection-name {
            margin: 0;
            font-size: 1rem;
            color: #fff;
        }

        .collection-count {
            margin: 4px 0 0;
            font-size: 0.875rem;
            color: #888;
        }

        .recent-generations {
            margin-top: 48px;
            padding: 1rem;
            background: #1a1a1a;
            border-radius: 8px;
        }

        .recent-generations h2 {
            color: white;
            margin-bottom: 24px;
            font-size: 1.25rem;
        }

        .generations-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 24px;
        }

        .generation-card {
            background: #2a2a2a;
            border-radius: 8px;
            overflow: hidden;
            transition: transform 0.2s;
        }

        .generation-card:hover {
            transform: translateY(-4px);
        }

        .generation-image {
            width: 100%;
            aspect-ratio: 1;
            object-fit: cover;
        }

        .generation-info {
            padding: 1rem;
        }

        .generation-prompt {
            color: #fff;
            font-size: 0.9rem;
            margin: 0.5rem 0;
        }

        .generation-actions {
            display: flex;
            gap: 0.5rem;
            margin-top: 0.5rem;
        }

        .generation-action {
            flex: 1;
            background: #4CAF50;
            color: white;
            border: none;
            padding: 0.5rem;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .generation-action:hover {
            background: #45a049;
        }

        .collection-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .back-button {
            background: transparent;
            border: 1px solid #555;
            color: #fff;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.2s;
        }

        .back-button:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .collection-card {
            cursor: pointer;
            transition: transform 0.2s;
        }

        .collection-card:hover {
            transform: translateY(-4px);
        }

        .create-new .collection-preview {
            border: 2px dashed #3a3a3a;
            background: transparent;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            gap: 8px;
            color: #999;
            transition: all 0.2s;
        }

        .create-new .collection-preview i {
            font-size: 24px;
        }

        .create-new:hover .collection-preview {
            border-color: #4CAF50;
            color: #4CAF50;
        }

        .create-new .collection-info {
            background: transparent;
        }

        .model-selector {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #333;
            border-radius: 8px;
            background: #1a1a1a;
            color: #fff;
            margin-bottom: 1.5rem;
            font-family: inherit;
            cursor: pointer;
        }

        .model-selector:focus {
            outline: none;
            border-color: #4CAF50;
        }

        .model-selector option {
            background: #1a1a1a;
            color: #fff;
            padding: 0.5rem;
        }

        /* Model selector styles */
        .custom-select {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #333;
            border-radius: 8px;
            background: #1a1a1a;
            color: #fff;
            font-size: 14px;
            cursor: pointer;
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 8px center;
            background-size: 16px;
            padding-right: 32px;
            margin-bottom: 1rem;
        }

        .custom-select:focus {
            outline: none;
            border-color: #4CAF50;
        }

        .custom-select option {
            background: #1a1a1a;
            color: #fff;
            padding: 8px;
        }

        .style-section {
            width: 100%;
            max-width: 100%;
            margin: 0 auto;
        }

        .style-section h3 {
            color: #fff;
            font-size: 18px;
            font-weight: 500;
            margin-bottom: 12px;
        }

        .hidden {
            display: none;
        }

        .input-group {
            margin-bottom: 1.5rem;
        }

        .input-group label {
            display: block;
            color: #fff;
            margin-bottom: 0.5rem;
            font-size: 1rem;
        }

        .input-group textarea {
            width: 100%;
            background: #1a1a1a;
            border: 1px solid #333;
            color: #fff;
            padding: 8px;
            border-radius: 4px;
            min-height: 60px;
            resize: vertical;
        }

        /* Style selector styles */
        .style-selector {
            margin: 1rem 0;
        }

        .style-carousel {
            display: flex;
            gap: 1rem;
            overflow-x: auto;
            padding: 1rem 0;
            scrollbar-width: thin;
            scrollbar-color: #4CAF50 #1a1a1a;
            -webkit-overflow-scrolling: touch;
        }

        .style-carousel::-webkit-scrollbar {
            height: 8px;
        }

        .style-carousel::-webkit-scrollbar-track {
            background: #1a1a1a;
            border-radius: 4px;
        }

        .style-carousel::-webkit-scrollbar-thumb {
            background: #4CAF50;
            border-radius: 4px;
        }

        .style-item {
            flex: 0 0 auto;
            width: 150px;
            padding: 10px;
            border: 2px solid transparent;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
            text-align: center;
        }

        .style-item:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .style-item.selected {
            border-color: #4CAF50;
            background: rgba(76, 175, 80, 0.1);
        }

        .style-item img {
            width: 100px;
            height: 100px;
            object-fit: cover;
            border-radius: 8px;
            margin-bottom: 8px;
        }

        .style-name {
            font-size: 14px;
            font-weight: 500;
            color: #fff;
        }

        .style-carousel {
            display: flex;
            gap: 16px;
            overflow-x: auto;
            padding: 10px;
            scroll-behavior: smooth;
            -ms-overflow-style: none;
            scrollbar-width: none;
        }

        /* Navigation buttons for the carousel */
        .style-carousel-container {
            position: relative;
            padding: 0 2rem;
        }

        .carousel-nav {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(26, 26, 26, 0.8);
            border: none;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s;
            z-index: 1;
        }

        .carousel-nav:hover {
            background: rgba(76, 175, 80, 0.8);
        }

        .carousel-nav.prev {
            left: 0;
        }

        .carousel-nav.next {
            right: 0;
        }

        .carousel-nav i {
            color: #fff;
            font-size: 1.2rem;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .style-item {
                width: 120px;
            }

            .style-item img {
                height: 100px;
            }
        }

        /* Loading animation styles */
        .loading-container {
            display: none;
            background: rgba(0, 0, 0, 0.8);
            padding: 2rem;
            border-radius: 8px;
            text-align: center;
            margin: 2rem auto;
            max-width: 400px;
        }

        .loading-container.visible {
            display: block;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 3px solid #333;
            border-radius: 50%;
            border-top-color: #4CAF50;
            margin: 0 auto 1rem;
            animation: spin 1s linear infinite;
        }

        .loading-text {
            color: #fff;
            font-size: 1.1rem;
            margin: 0;
        }

        .loading-subtext {
            color: #999;
            font-size: 0.9rem;
            margin-top: 0.5rem;
        }

        @keyframes spin {
            to {
                transform: rotate(360deg);
            }
        }

        /* Progress bar styles */
        .progress-bar {
            width: 100%;
            height: 4px;
            background: #333;
            border-radius: 2px;
            margin-top: 1rem;
            overflow: hidden;
        }

        .progress-bar-fill {
            height: 100%;
            background: #4CAF50;
            width: 0%;
            transition: width 0.3s ease;
            animation: progress 2s ease-in-out infinite;
        }

        @keyframes progress {
            0% {
                width: 0%;
            }
            50% {
                width: 70%;
            }
            100% {
                width: 100%;
            }
        }

        .form-container {
            background: #1a1a1a;
            padding: 24px;
            border-radius: 12px;
            margin-bottom: 24px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #fff;
            font-weight: 500;
        }

        .form-group input[type="text"] {
            width: 100%;
            padding: 12px;
            border: 1px solid #333;
            border-radius: 6px;
            background: #222;
            color: #fff;
            font-size: 1rem;
            transition: border-color 0.3s;
        }

        .form-group input[type="text"]:focus {
            border-color: #0066ff;
            outline: none;
        }

        .form-group input[type="text"]::placeholder {
            color: #666;
        }

        /* Generation Card Styles */
        .generation-card {
            position: relative;
            width: 100%;
            max-width: 512px;
            margin: 0 auto;
            background: #1a1a1a;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .image-container {
            position: relative;
            width: 100%;
            padding-top: 100%; /* 1:1 Aspect Ratio */
        }

        .generation-image {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: contain;
            background: #2a2a2a;
        }

        .menu-overlay {
            position: absolute;
            top: 8px;
            right: 8px;
            display: flex;
            gap: 8px;
            opacity: 0;
            transition: opacity 0.2s ease;
        }

        .image-container:hover .menu-overlay {
            opacity: 1;
        }

        .menu-button {
            background: rgba(0, 0, 0, 0.7);
            border: none;
            border-radius: 50%;
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            color: white;
            transition: background-color 0.2s ease;
        }

        .menu-button:hover {
            background: rgba(0, 0, 0, 0.9);
        }

        .menu-button i {
            font-size: 16px;
        }

        /* Loading Container Styles */
        .loading-container {
            display: none;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .loading-container.visible {
            display: flex;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Error Message Styles */
        .error {
            padding: 20px;
            text-align: center;
            color: #ff6b6b;
        }

        /* Style Item Styles */
        .style-item {
            flex: 0 0 150px;
            margin: 0 10px;
            padding: 10px;
            border-radius: 12px;
            background: #1a1a1a;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .style-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        .style-item.selected {
            border-color: #4CAF50;
            box-shadow: 0 0 15px rgba(76, 175, 80, 0.3);
        }

        .style-preview {
            width: 100%;
            height: 150px;
            object-fit: cover;
            border-radius: 8px;
            margin-bottom: 8px;
        }

        .style-name {
            font-size: 14px;
            color: #fff;
            text-align: center;
            margin-top: 8px;
        }

        /* Style Carousel */
        .style-carousel-container {
            position: relative;
            width: 100%;
            margin: 20px 0;
            padding: 0 40px;
        }

        .style-carousel {
            display: flex;
            overflow-x: auto;
            scroll-behavior: smooth;
            -webkit-overflow-scrolling: touch;
            scrollbar-width: none;
            -ms-overflow-style: none;
            padding: 10px 0;
        }

        .style-carousel::-webkit-scrollbar {
            display: none;
        }

        .carousel-nav {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(0, 0, 0, 0.7);
            border: none;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.2s ease;
            z-index: 2;
        }

        .carousel-nav:hover {
            background: rgba(0, 0, 0, 0.9);
        }

        .carousel-nav.prev {
            left: 0;
        }

        .carousel-nav.next {
            right: 0;
        }

        .menu-overlay {
            position: absolute;
            top: 10px;
            right: 10px;
            display: flex;
            gap: 8px;
            opacity: 0;
            transition: opacity 0.2s ease;
            z-index: 10;
            padding: 8px;
            border-radius: 8px;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(4px);
        }

        .image-container {
            position: relative;
            width: 100%;
            aspect-ratio: 1;
        }

        .image-container:hover .menu-overlay {
            opacity: 1;
        }

        .menu-button {
            background: rgba(0, 0, 0, 0.7);
            border: none;
            border-radius: 50%;
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            color: white;
            transition: background-color 0.2s ease;
        }

        .menu-button:hover {
            background: rgba(0, 0, 0, 0.9);
            transform: translateY(-2px);
        }

        .menu-button i {
            font-size: 16px;
        }

        .generation-card {
            border-radius: 12px;
            overflow: hidden;
            background: #1a1a1a;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .generation-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
            display: block;
        }

        .color-picker-container {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .color-picker {
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
            width: 60px;
            height: 40px;
            background-color: transparent;
            border: none;
            cursor: pointer;
        }

        .color-picker::-webkit-color-swatch-wrapper {
            padding: 0;
        }

        .color-picker::-webkit-color-swatch {
            border: 2px solid #333;
            border-radius: 8px;
        }

        .color-hex {
            font-family: monospace;
            font-size: 1rem;
            color: #fff;
            background: #1a1a1a;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            border: 1px solid #333;
        }

        .background-selector {
            display: flex;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .background-option {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            border: 2px solid #333;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .background-option.selected {
            border-color: #4CAF50;
        }

        .background-option:hover {
            border-color: #4CAF50;
        }

        .color-square {
            width: 20px;
            height: 20px;
            border-radius: 4px;
            border: 1px solid #333;
        }

        .color-square.light {
            background-color: #ffffff;
        }

        .color-square.dark {
            background-color: #000000;
        }

        .tooltip-icon {
            display: inline-block;
            width: 16px;
            height: 16px;
            background-color: #007bff;
            color: white;
            border-radius: 50%;
            text-align: center;
            line-height: 16px;
            font-size: 12px;
            font-style: normal;
            font-weight: bold;
            cursor: help;
            margin-left: 5px;
            vertical-align: middle;
        }

        /* Switch styles */
        .switch-container {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }

        .switch-label {
            display: flex;
            align-items: center;
            cursor: pointer;
            color: #fff;
        }

        .switch-label input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .switch-custom {
            position: relative;
            display: inline-block;
            width: 40px;
            height: 20px;
            background-color: #333;
            border-radius: 20px;
            margin-right: 10px;
            transition: background-color 0.3s;
        }

        .switch-custom:before {
            position: absolute;
            content: "";
            height: 16px;
            width: 16px;
            left: 2px;
            bottom: 2px;
            background-color: white;
            border-radius: 50%;
            transition: transform 0.3s;
        }

        input:checked + .switch-custom {
            background-color: #4CAF50;
        }

        input:checked + .switch-custom:before {
            transform: translateX(20px);
        }

        .template-preview {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 10px;
            text-align: center;
        }

        .template-preview i {
            font-size: 24px;
            margin-bottom: 10px;
            color: #4CAF50;
        }

        .template-preview img {
            width: 100%;
            height: 80px;
            object-fit: contain;
            margin-bottom: 5px;
            border-radius: 4px;
            background-color: #333;
        }

        .template-name {
            font-size: 12px;
            font-weight: 500;
            margin-top: 5px;
            color: #fff;
            text-align: center;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            width: 100%;
        }

        .empty-message {
            color: #888;
            text-align: center;
            padding: 20px;
            font-size: 14px;
        }

        .error-message {
            color: #ff6b6b;
            text-align: center;
            padding: 20px;
            font-size: 14px;
        }

        /* Add styles for the text list component */
        text-list {
            display: block;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <div id="topbar"></div>

    <div id="generator" class="container">
        <h3 id="main-title">Design Your Sticker</h3>
        
      
        
        <div class="style-section">
            <div class="form-group">
                <label for="objectInput">What would you like to create?</label>
                <input type="text" id="objectInput" placeholder="e.g., cat, dog, dragon">
            </div>

            <div class="form-group" style="margin-bottom: 15px;">
                <div style="display: flex; flex-direction: row; flex-wrap: nowrap; align-items: center;">
                    <div style="margin-right: 5px;">Include Text</div>
                    <div style="margin-right: 5px;"><input type="checkbox" id="includeTextSwitch"></div>
                    <div><span class="tooltip-icon" title="Enable to add text to your design. When disabled, any text instructions in parentheses will be removed from the prompt.">ⓘ</span></div>
                </div>
            </div>

            <div id="textInputGroup" class="form-group" style="display: none;">
                <label>
                    Text for the Design
                    <span class="tooltip-icon" title="Add up to 3 text elements that will be included in your design. These will be referenced as [text1], [text2], and [text3] in templates.">ⓘ</span>
                </label>
                <text-list id="textList"></text-list>
            </div>

            <div class="form-group">
                <label>Choose a Prompt Template</label>
                <div class="style-carousel-container">
                    <button class="carousel-nav prev" onclick="scrollCarousel('templates', 'left')">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <div id="templateCarousel" class="style-carousel">
                        <!-- Templates will be loaded here -->
                    </div>
                    <button class="carousel-nav next" onclick="scrollCarousel('templates', 'right')">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </div>

            <div class="form-group">
                <label for="model">Choose Model</label>
                <select id="model" class="custom-select">
                    <option value="">Loading models...</option>
                </select>
            </div>

            <div class="form-group">
                <label>
                    Choose Color Palette
                    <span class="tooltip-icon" title="Select a color palette that will influence the AI image generation. This will be referenced as [image-palette] in templates.">ⓘ</span>
                </label>
                <color-palette-selector id="image-palette-selector"></color-palette-selector>
            </div>

            <div class="form-group">
                <label for="theme">Choose Theme</label>
                <select id="theme" class="custom-select">
                    <option value="">Loading themes...</option>
                </select>
            </div>

            <div class="form-group">
                <label>Background</label>
                <div class="background-selector">
                    <div class="background-option light" data-bg="light" onclick="selectBackground('light')">
                        <div class="color-square light"></div>
                        <span>Light</span>
                    </div>
                    <div class="background-option dark" data-bg="dark" onclick="selectBackground('dark')">
                        <div class="color-square dark"></div>
                        <span>Dark</span>
                    </div>
                    <div class="background-option transparent" data-bg="transparent" onclick="selectBackground('transparent')">
                        <div class="color-square" style="background-color: transparent;"></div>
                        <span>Transparent</span>
                    </div>
                </div>
            </div>

            <div class="form-group" style="display: none;">
                <label>Choose a Style (Optional)</label>
                <div class="style-carousel-container">
                    <button class="carousel-nav prev" onclick="scrollCarousel('styles', 'left')">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <div id="styleCarousel" class="style-carousel">
                        <!-- Styles will be loaded here -->
                    </div>
                    <button class="carousel-nav next" onclick="scrollCarousel('styles', 'right')">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </div>

            <div class="form-group text-center">
                <button id="generateBtn" class="btn btn-primary btn-lg">
                    Generate Image
                </button>
            </div>
        </div>

        <div id="result" class="result">
            <div id="loadingContainer" class="loading-container">
                <div class="loading-spinner"></div>
            </div>
            <div id="generatedImage" class="generations-grid">
                <!-- Generated images will be added here as generation-card elements -->
            </div>
        </div>
    </div>

    <div id="collections" class="hidden">
        <div class="container">
            <h1>Your Collections</h1>
            <div class="collections-grid">
                <div class="collection-card create-new">
                    <div class="collection-preview">
                        <div class="preview-placeholder">
                            <i class="fas fa-plus"></i>
                            <span>Create New Collection</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Collection View Section -->
    <div id="collection-view" class="hidden">
        <div class="container">
            <div class="collection-header">
                <button class="back-button" onclick="window.history.back()">
                    <i class="fas fa-arrow-left"></i> Back to Collections
                </button>
                <h1 id="collection-name"></h1>
            </div>
            <div class="generations-grid" id="collection-images"></div>
        </div>
    </div>

    <!-- Collection Modal -->
    <div id="newCollectionModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Add to Collection</h2>
                <button class="close-modal">&times;</button>
            </div>
            <div class="modal-body">
                <div id="existingCollections">
                    <h3>Select Collection</h3>
                    <div class="collections-list"></div>
                </div>
                <div class="divider">or</div>
                <div id="newCollection">
                    <h3>Create New Collection</h3>
                    <input type="text" id="newCollectionName" placeholder="Collection name">
                    <button class="btn-primary create-collection">Create</button>
                </div>
            </div>
        </div>
    </div>

    <collection-modal></collection-modal>
    <new-collection-modal></new-collection-modal>

    <script type="module" src="/js/components/GenerationCard.js"></script>
    <script type="module" src="/js/components/Topbar.js"></script>
    <script type="module" src="/js/components/TextList.js"></script>
    <script type="module" src="/js/components/ColorPaletteSelector.js"></script>

    <script>
        // Global variables for tracking state
        let currentModel = 'flux-dreamscape';
        let currentSection = 'generator';
        let selectedBackground = 'light';
        let selectedStyle = '';
        let selectedTheme = null; // Store selected theme data
        let selectedTemplateId = '';
        let selectedTemplate = null; // Store the full template object
        let selectedImagePalette = null; // Store selected image palette

        // Function to show different sections
        function showSection(sectionId) {
            const sections = ['generator', 'collections', 'collection-view'];
            sections.forEach(section => {
                const element = document.getElementById(section);
                if (element) {
                    if (section === sectionId) {
                        element.classList.remove('hidden');
                    } else {
                        element.classList.add('hidden');
                    }
                }
            });
            currentSection = sectionId;
        }

        // Function to select background
        function selectBackground(bg) {
            selectedBackground = bg;
            
            // Update UI
            document.querySelectorAll('.background-option').forEach(option => {
                if (option.dataset.bg === bg) {
                    option.classList.add('selected');
                } else {
                    option.classList.remove('selected');
                }
            });
        }

        // Function to select style
        function selectStyle(element) {
            // Remove selected class from all styles
            document.querySelectorAll('.style-item').forEach(item => {
                item.classList.remove('selected');
            });
            
            // Add selected class to clicked style
            element.classList.add('selected');
            
            // Store the selected style ID
            selectedStyle = element.dataset.styleId;
        }

        // Function to select template
        async function selectTemplate(element) {
            // Remove selected class from all templates
            document.querySelectorAll('.style-item[data-template-id]').forEach(item => {
                item.classList.remove('selected');
            });
            
            // Add selected class to clicked template
            element.classList.add('selected');
            
            // Store the selected template ID
            selectedTemplateId = element.dataset.templateId;
            console.log('Selected template:', selectedTemplateId);
            
            try {
                // Fetch template details from API
                const response = await fetch(`/api/templates/${selectedTemplateId}`);
                if (!response.ok) {
                    throw new Error(`Failed to load template: ${response.status} ${response.statusText}`);
                }
                
                selectedTemplate = await response.json();
                console.log('Found template from API:', selectedTemplate);

                // Make sure we store the template content as the prompt
                selectedTemplate.prompt = selectedTemplate.template;

                // Update the prompt input field if it exists
                const promptInput = document.getElementById('promptInput');
                if (promptInput && selectedTemplate.template) {
                    promptInput.value = selectedTemplate.template;
                }

                // Set the original palette description from the template
                const paletteSelector = document.getElementById('image-palette-selector');
                if (paletteSelector && selectedTemplate.originalPalette) {
                    paletteSelector.setOriginalPaletteDescription(selectedTemplate.originalPalette);
                    console.log('Set original palette from template:', selectedTemplate.originalPalette);
                }
                
                // If the template has randomOptions, update any relevant form fields
                if (selectedTemplate.randomOptions) {
                    // Handle random options here if needed for your application
                    console.log('Template random options:', selectedTemplate.randomOptions);
                }
            } catch (error) {
                console.error('Error fetching template:', error);
                alert(`Error loading template: ${error.message}`);
            }
        }

        // Function to scroll the carousel
        function scrollCarousel(type, direction) {
            const carousel = document.getElementById(`${type}Carousel`);
            const scrollAmount = 200;
            
            if (direction === 'left') {
                carousel.scrollLeft -= scrollAmount;
            } else {
                carousel.scrollLeft += scrollAmount;
            }
        }

        // Function to toggle text input visibility
        function toggleTextInputVisibility() {
            const includeTextSwitch = document.getElementById('includeTextSwitch');
            const textInputGroup = document.getElementById('textInputGroup');
            
            if (includeTextSwitch && textInputGroup) {
                textInputGroup.style.display = includeTextSwitch.checked ? 'block' : 'none';
            }
        }

        // Add event listener for the Include Text switch
        document.addEventListener('DOMContentLoaded', () => {
            const includeTextSwitch = document.getElementById('includeTextSwitch');
            if (includeTextSwitch) {
                includeTextSwitch.addEventListener('change', toggleTextInputVisibility);
                // Initialize visibility
                toggleTextInputVisibility();
            }
        });

        // Make all functions globally available
        window.showSection = showSection;
        window.selectBackground = selectBackground;
        window.selectStyle = selectStyle;
        window.selectTemplate = selectTemplate;
        window.scrollCarousel = scrollCarousel;
        window.toggleTextInputVisibility = toggleTextInputVisibility;
    </script>

    <script type="module">
        import { createTopbar } from '/js/components/Topbar.js';
        import { GenerationCard } from '/js/components/GenerationCard.js';
        import { CollectionModal } from '/js/components/CollectionModal.js';
        
        // Initialize everything when DOM is loaded
        document.addEventListener('DOMContentLoaded', async () => {
            try {
                // Initialize topbar
                const topbar = document.getElementById('topbar');
                if (topbar) {
                    await createTopbar(topbar);
                }

                // Load initial data
                await Promise.all([
                    loadModels(),
                    loadThemes(),
                    loadStyles(),
                    loadTemplates()
                ]);

                // Show initial section
                showSection('generator');

                // Add event listener to generate button
                const generateBtn = document.getElementById('generateBtn');
                if (generateBtn) {
                    generateBtn.addEventListener('click', generateImage);
                }

                // Initialize palette selector
                const paletteSelector = document.getElementById('image-palette-selector');

                if (paletteSelector) {
                    paletteSelector.addEventListener('paletteChange', function(e) {
                        selectedImagePalette = e.detail.palette;
                        console.log('Image palette changed:', selectedImagePalette);
                    });
                }
            } catch (error) {
                console.error('Error during initialization:', error);
            }
        });

        // Function to load models
        async function loadModels() {
            const modelSelector = document.getElementById('model');
            if (!modelSelector) {
                console.error('Model selector not found');
                return;
            }

            try {
                // Show loading state
                modelSelector.innerHTML = '<option value="">Loading models...</option>';
                modelSelector.disabled = true;

                // Fetch models from API
                const response = await fetch('/api/models');
                if (!response.ok) {
                    throw new Error('Failed to load models');
                }
                const models = await response.json();
                console.log('Loaded models:', models);

                // Clear loading state
                modelSelector.innerHTML = '';
                modelSelector.disabled = false;

                // Add each model as an option
                models.forEach(model => {
                    const option = document.createElement('option');
                    option.value = model.name;
                    option.text = model.displayName;
                    modelSelector.appendChild(option);
                });

                // Set default model
                const defaultModel = models.find(m => m.name === 'flux-yarn-art') || models[0];
                if (defaultModel) {
                    modelSelector.value = defaultModel.name;
                    currentModel = defaultModel.name;
                }
            } catch (error) {
                console.error('Error loading models:', error);
                modelSelector.innerHTML = '<option value="">Error loading models</option>';
                modelSelector.disabled = true;
            }
        }

        // Function to load themes
        async function loadThemes() {
            try {
                const response = await fetch('/api/themes');
                if (!response.ok) {
                    throw new Error('Failed to load themes');
                }
                const themes = await response.json();
                
                const themeSelector = document.getElementById('theme');
                if (!themeSelector) {
                    console.error('Theme selector not found');
                    return;
                }

                // Store themes data
                window.themes = themes;

                themeSelector.innerHTML = `
                    <option value="">Select a theme...</option>
                    ${themes.map(theme => `
                        <option value="${theme._id}">${theme.name}</option>
                    `).join('')}
                `;

                // Add change event listener
                themeSelector.addEventListener('change', (e) => {
                    const themeId = e.target.value;
                    selectedTheme = themeId ? themes.find(t => t._id === themeId) : null;
                });
            } catch (error) {
                console.error('Error loading themes:', error);
                const themeSelector = document.getElementById('theme');
                if (themeSelector) {
                    themeSelector.innerHTML = '<option value="">Error loading themes</option>';
                }
            }
        }

        // Function to load styles
        async function loadStyles() {
            try {
                const response = await fetch('/api/styles');
                if (!response.ok) {
                    throw new Error('Failed to load styles');
                }
                const styles = await response.json();
                
                const styleCarousel = document.getElementById('styleCarousel');
                if (!styleCarousel) {
                    console.error('Style carousel not found');
                    return;
                }

                styleCarousel.innerHTML = styles.map(style => `
                    <div class="style-item" data-style-id="${style._id}" onclick="selectStyle(this)">
                        <img src="${style.imageUrl}" alt="${style.name}">
                        <div class="style-name">${style.name}</div>
                    </div>
                `).join('');
            } catch (error) {
                console.error('Error loading styles:', error);
                const styleCarousel = document.getElementById('styleCarousel');
                if (styleCarousel) {
                    styleCarousel.innerHTML = '<div class="error-message">Error loading styles</div>';
                }
            }
        }

        // Function to load templates
        async function loadTemplates() {
            try {
                // Get templates from API instead of localStorage
                const response = await fetch('/api/templates');
                if (!response.ok) {
                    throw new Error('Failed to load templates from API');
                }
                const templates = await response.json();
                
                const templateCarousel = document.getElementById('templateCarousel');
                if (!templateCarousel) {
                    console.error('Template carousel not found');
                    return;
                }

                if (templates.length === 0) {
                    templateCarousel.innerHTML = '<div class="empty-message">No templates available. Create templates in the Templates section.</div>';
                    return;
                }

                console.log('Loaded templates from API:', templates);
                
                templateCarousel.innerHTML = templates.map(template => {
                    // Check if template has a thumbnail
                    const hasThumbnail = template.thumbnailUrl && template.thumbnailUrl.trim() !== '';
                    
                    return `
                        <div class="style-item" data-template-id="${template._id}" onclick="selectTemplate(this)">
                            <div class="template-preview">
                                ${hasThumbnail 
                                    ? `<img src="${template.thumbnailUrl}" alt="${template.name}">`
                                    : `<i class="fas fa-file-alt"></i>`
                                }
                            </div>
                            <div class="template-name">${template.name}</div>
                        </div>
                    `;
                }).join('');
            } catch (error) {
                console.error('Error loading templates:', error);
                const templateCarousel = document.getElementById('templateCarousel');
                if (templateCarousel) {
                    templateCarousel.innerHTML = '<div class="error-message">Error loading templates: ${error.message}</div>';
                }
            }
        }

        // Function to generate image
        async function generateImage() {
            const generateBtn = document.getElementById('generateBtn');
            const originalBtnText = generateBtn.innerHTML;
            
            try {
                // Show loading state
                generateBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Generating...';
                generateBtn.disabled = true;
                
                // Get form values
                const objectInput = document.getElementById('objectInput');
                const modelSelector = document.getElementById('model');
                const themeSelector = document.getElementById('theme');
                
                // Get selected style
                const styleElement = document.querySelector('.style-item.selected');
                
                // Get Include Text switch state
                const includeTextSwitch = document.getElementById('includeTextSwitch');
                const includeText = includeTextSwitch ? includeTextSwitch.checked : false;
                
                // Get individual text values from text-list component
                let text1 = '';
                let text2 = '';
                let text3 = '';
                
                if (includeText) {
                    const textList = document.getElementById('textList');
                    text1 = textList ? textList.text1 : '';
                    text2 = textList ? textList.text2 : '';
                    text3 = textList ? textList.text3 : '';
                    console.log('Text values:', { text1, text2, text3 });
                }
                
                // Get template if using prompt templates
                let templateRandomOptions = null;
                let templateId = null;
                let templatePrompt = null;
                
                if (selectedTemplate) {
                    templateId = selectedTemplate._id;
                    templatePrompt = selectedTemplate.template;
                    templateRandomOptions = selectedTemplate.randomOptions;
                    console.log('Using template:', {
                        id: templateId,
                        prompt: templatePrompt,
                        randomOptions: templateRandomOptions
                    });
                }

                // Prepare request data
                const requestData = {
                    prompt: objectInput.value.trim(),
                    model: modelSelector.value,
                    text1: text1,
                    text2: text2,
                    text3: text3,
                    style: styleElement?.dataset.styleId,
                    theme: themeSelector?.value,
                    background: selectedBackground || 'light',
                    randomOptions: templateRandomOptions,
                    templateId: templateId,
                    templatePrompt: templatePrompt,
                    noText: !includeText,
                    imagePalette: selectedImagePalette
                };

                console.log('🎨 FRONT-END DEBUG - Generating with:', requestData);
                console.log('🎨 FRONT-END DEBUG - Template info:', {
                    selectedTemplateId: selectedTemplateId,
                    selectedTemplate: selectedTemplate,
                    templateId: templateId,
                    originalPalette: selectedTemplate?.originalPalette
                });
                console.log('🎨 FRONT-END DEBUG - Image palette info:', {
                    selectedImagePalette: selectedImagePalette,
                    paletteId: selectedImagePalette?.id,
                    paletteDescription: selectedImagePalette?.description
                });

                // Fetch API
                const response = await fetch('/api/generate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                });

                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.error || 'Failed to generate image');
                }

                const data = await response.json();

                // Create and add the generation card
                const card = document.createElement('generation-card');
                card.setAttribute('image-url', data.imageUrl);
                card.setAttribute('prompt', data.prompt);
                card.setAttribute('generation-id', data.generationId);
                
                const generatedImage = document.getElementById('generatedImage');
                if (generatedImage) {
                    generatedImage.insertBefore(card, generatedImage.firstChild);
                }

            } catch (error) {
                console.error('Generation error:', error);
                alert(error.message || 'Failed to generate image');
            } finally {
                // Reset loading state
                generateBtn.innerHTML = originalBtnText;
                generateBtn.disabled = false;
            }
        }

        // Function to select template
        async function selectTemplate(element) {
            // Remove selected class from all templates
            document.querySelectorAll('.style-item[data-template-id]').forEach(item => {
                item.classList.remove('selected');
            });
            
            // Add selected class to clicked template
            element.classList.add('selected');
            
            // Store the selected template ID
            selectedTemplateId = element.dataset.templateId;
            console.log('Selected template:', selectedTemplateId);
            
            try {
                // Fetch template details from API
                const response = await fetch(`/api/templates/${selectedTemplateId}`);
                if (!response.ok) {
                    throw new Error(`Failed to load template: ${response.status} ${response.statusText}`);
                }
                
                selectedTemplate = await response.json();
                console.log('Found template from API:', selectedTemplate);

                // Make sure we store the template content as the prompt
                selectedTemplate.prompt = selectedTemplate.template;

                // Update the prompt input field if it exists
                const promptInput = document.getElementById('promptInput');
                if (promptInput && selectedTemplate.template) {
                    promptInput.value = selectedTemplate.template;
                }

                // Set the original palette description from the template
                const paletteSelector = document.getElementById('image-palette-selector');
                if (paletteSelector && selectedTemplate.originalPalette) {
                    paletteSelector.setOriginalPaletteDescription(selectedTemplate.originalPalette);
                    console.log('Set original palette from template:', selectedTemplate.originalPalette);
                }
                
                // If the template has randomOptions, update any relevant form fields
                if (selectedTemplate.randomOptions) {
                    // Handle random options here if needed for your application
                    console.log('Template random options:', selectedTemplate.randomOptions);
                }
            } catch (error) {
                console.error('Error fetching template:', error);
                alert(`Error loading template: ${error.message}`);
            }
        }

        // Function to scroll the carousel
        function scrollCarousel(type, direction) {
            const carousel = document.getElementById(`${type}Carousel`);
            const scrollAmount = 200;
            
            if (direction === 'left') {
                carousel.scrollLeft -= scrollAmount;
            } else {
                carousel.scrollLeft += scrollAmount;
            }
        }

        // Function to toggle text input visibility
        function toggleTextInputVisibility() {
            const includeTextSwitch = document.getElementById('includeTextSwitch');
            const textInputGroup = document.getElementById('textInputGroup');
            
            if (includeTextSwitch && textInputGroup) {
                textInputGroup.style.display = includeTextSwitch.checked ? 'block' : 'none';
            }
        }

        // Add event listener for the Include Text switch
        document.addEventListener('DOMContentLoaded', () => {
            const includeTextSwitch = document.getElementById('includeTextSwitch');
            if (includeTextSwitch) {
                includeTextSwitch.addEventListener('change', toggleTextInputVisibility);
                // Initialize visibility
                toggleTextInputVisibility();
            }
        });

        // Make all functions globally available
        window.showSection = showSection;
        window.selectBackground = selectBackground;
        window.selectStyle = selectStyle;
        window.selectTemplate = selectTemplate;
        window.scrollCarousel = scrollCarousel;
        window.toggleTextInputVisibility = toggleTextInputVisibility;
    </script>

    <script>
        // Function to handle adding to collection
        async function addToCollection(imageUrl, prompt) {
            const modal = document.createElement('collection-modal');
            modal.imageUrl = imageUrl;
            modal.prompt = prompt;
            document.body.appendChild(modal);
        }

        // Make functions globally available
        window.addToCollection = addToCollection;
    </script>

    <script>
        // Function to update admin credits
        async function updateAdminCredits(credits) {
            try {
                const response = await fetch('/api/admin/credits/update', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${localStorage.getItem('token')}`
                    },
                    body: JSON.stringify({ credits })
                });

                if (!response.ok) {
                    const error = await response.json();
                    throw new Error(error.error || 'Failed to update credits');
                }

                const data = await response.json();
                console.log('Admin credits updated:', data);
                
                // Update the credits display
                const creditsElement = document.getElementById('credits');
                if (creditsElement) {
                    creditsElement.textContent = data.credits;
                }

                // Close any open modals
                const modal = document.querySelector('.modal');
                if (modal) {
                    modal.style.display = 'none';
                }

                alert('Credits updated successfully!');
            } catch (error) {
                console.error('Error updating admin credits:', error);
                alert(error.message);
            }
        }

        // Add to window object
        window.updateAdminCredits = updateAdminCredits;
    </script>
</body>
</html>
