<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Color Intensity Save/Load</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .controls {
            margin: 10px 0;
        }
        .controls label {
            display: inline-block;
            width: 150px;
            font-weight: bold;
        }
        .controls select, .controls input {
            padding: 5px;
            margin: 5px;
        }
        .result {
            background: #f9f9f9;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            color: green;
            font-weight: bold;
        }
        .error {
            color: red;
            font-weight: bold;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a87;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Color Intensity Save/Load Test</h1>
        <p>This test verifies that Color Intensity values are properly saved and loaded in templates.</p>

        <div class="test-section">
            <h3>Test 1: Create Text Object with Color Intensity</h3>
            <div class="controls">
                <label>Text Content:</label>
                <input type="text" id="textContent" value="TEST TEXT" />
            </div>
            <div class="controls">
                <label>Color Intensity:</label>
                <select id="textColorIntensity">
                    <option value="no-change">No Change</option>
                    <option value="light">Light</option>
                    <option value="medium">Medium</option>
                    <option value="dark">Dark</option>
                </select>
            </div>
            <button onclick="createTextObject()">Create Text Object</button>
            <div class="result" id="textResult"></div>
        </div>

        <div class="test-section">
            <h3>Test 2: Create Image Object with Color Intensity</h3>
            <div class="controls">
                <label>Image URL:</label>
                <input type="text" id="imageUrl" value="/stock/shapes/hand-drawn-dividers/4.svg" />
            </div>
            <div class="controls">
                <label>Color Intensity:</label>
                <select id="imageColorIntensity">
                    <option value="no-change">No Change</option>
                    <option value="light">Light</option>
                    <option value="medium">Medium</option>
                    <option value="dark">Dark</option>
                </select>
            </div>
            <button onclick="createImageObject()">Create Image Object</button>
            <div class="result" id="imageResult"></div>
        </div>

        <div class="test-section">
            <h3>Test 3: Serialize and Deserialize Objects</h3>
            <button onclick="serializeObjects()">Serialize Objects</button>
            <button onclick="deserializeObjects()">Deserialize Objects</button>
            <button onclick="clearObjects()">Clear Objects</button>
            <div class="result" id="serializeResult"></div>
        </div>

        <div class="test-section">
            <h3>Test Results</h3>
            <div class="result" id="finalResult"></div>
        </div>
    </div>

    <script>
        // Mock the design editor functions we need for testing
        let testObjects = [];
        let serializedData = null;
        let nextId = 1;

        // Mock createTextObject function (simplified version)
        function createTextObject(options = {}) {
            const defaults = {
                id: nextId++,
                type: 'text',
                text: "TEXT",
                x: 100,
                y: 100,
                color: "#3b82f6",
                fontFamily: "Arial",
                fontSize: 24,
                colorIntensity: 'no-change' // This is the fix we're testing
            };
            return { ...defaults, ...options };
        }

        // Mock createImageObject function (simplified version)
        function createImageObject(options = {}) {
            const defaults = {
                id: nextId++,
                type: 'image',
                imageUrl: '/placeholder.png',
                x: 100,
                y: 100,
                scale: 1.0,
                colorIntensity: 'no-change' // Already existed for images
            };
            return { ...defaults, ...options };
        }

        // Test functions
        function createTextObject() {
            const text = document.getElementById('textContent').value;
            const colorIntensity = document.getElementById('textColorIntensity').value;
            
            const textObj = createTextObject({
                text: text,
                colorIntensity: colorIntensity
            });
            
            testObjects.push(textObj);
            
            document.getElementById('textResult').textContent = 
                `Created text object:\n${JSON.stringify(textObj, null, 2)}`;
        }

        function createImageObject() {
            const imageUrl = document.getElementById('imageUrl').value;
            const colorIntensity = document.getElementById('imageColorIntensity').value;
            
            const imageObj = createImageObject({
                imageUrl: imageUrl,
                colorIntensity: colorIntensity
            });
            
            testObjects.push(imageObj);
            
            document.getElementById('imageResult').textContent = 
                `Created image object:\n${JSON.stringify(imageObj, null, 2)}`;
        }

        function serializeObjects() {
            if (testObjects.length === 0) {
                document.getElementById('serializeResult').textContent = 'No objects to serialize. Create some objects first.';
                return;
            }

            serializedData = JSON.stringify(testObjects, null, 2);
            document.getElementById('serializeResult').textContent = 
                `Serialized ${testObjects.length} objects:\n${serializedData}`;
        }

        function deserializeObjects() {
            if (!serializedData) {
                document.getElementById('serializeResult').textContent = 'No serialized data. Serialize objects first.';
                return;
            }

            try {
                const deserializedObjects = JSON.parse(serializedData);
                
                // Simulate the template loading process
                const restoredObjects = deserializedObjects.map(objData => {
                    if (objData.type === 'text') {
                        return createTextObject(objData);
                    } else if (objData.type === 'image') {
                        return createImageObject(objData);
                    }
                    return objData;
                });

                // Check if colorIntensity values are preserved
                let allPreserved = true;
                let results = [];
                
                for (let i = 0; i < testObjects.length; i++) {
                    const original = testObjects[i];
                    const restored = restoredObjects[i];
                    
                    const preserved = original.colorIntensity === restored.colorIntensity;
                    allPreserved = allPreserved && preserved;
                    
                    results.push({
                        id: original.id,
                        type: original.type,
                        original: original.colorIntensity,
                        restored: restored.colorIntensity,
                        preserved: preserved
                    });
                }

                document.getElementById('serializeResult').textContent = 
                    `Deserialized objects:\n${JSON.stringify(restoredObjects, null, 2)}`;

                // Show final test results
                const finalResultDiv = document.getElementById('finalResult');
                finalResultDiv.innerHTML = `
                    <h4>Color Intensity Preservation Test Results:</h4>
                    ${results.map(r => `
                        <div>
                            Object ${r.id} (${r.type}): 
                            ${r.original} → ${r.restored} 
                            <span class="${r.preserved ? 'success' : 'error'}">
                                ${r.preserved ? '✓ PRESERVED' : '✗ LOST'}
                            </span>
                        </div>
                    `).join('')}
                    <div style="margin-top: 10px;">
                        <strong class="${allPreserved ? 'success' : 'error'}">
                            Overall Result: ${allPreserved ? 'ALL COLOR INTENSITY VALUES PRESERVED ✓' : 'SOME COLOR INTENSITY VALUES LOST ✗'}
                        </strong>
                    </div>
                `;

            } catch (error) {
                document.getElementById('serializeResult').textContent = `Error deserializing: ${error.message}`;
            }
        }

        function clearObjects() {
            testObjects = [];
            serializedData = null;
            nextId = 1;
            document.getElementById('textResult').textContent = '';
            document.getElementById('imageResult').textContent = '';
            document.getElementById('serializeResult').textContent = '';
            document.getElementById('finalResult').textContent = '';
        }

        // Initialize with some default values for quick testing
        document.getElementById('textColorIntensity').value = 'medium';
        document.getElementById('imageColorIntensity').value = 'dark';
    </script>
</body>
</html>
